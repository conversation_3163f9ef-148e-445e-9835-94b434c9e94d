<template>
  <div class="countdown-container">
    <span :style="computedValueStyle">{{ displayTime }}</span>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

// 扩展dayjs插件
dayjs.extend(utc)
dayjs.extend(timezone)

// 定义props
const props = defineProps({
  value: {
    type: [Number, Object], // number为时间戳，Object为dayjs对象
    required: true
  },
  format: {
    type: String,
    default: 'HH:mm:ss'
  },
  valueStyle: {
    type: Object,
    default: () => ({})
  },
  timezone: {
    type: String,
    default: 'Asia/Shanghai'
  }
})

// 定义事件
const emit = defineEmits(['finish'])

// 生成唯一实例ID
const instanceId = `countdown_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

// 响应式数据
const displayTime = ref('00:00:00')
const isFinished = ref(false)
const isActive = ref(true)

// 定时器相关变量（每个实例独立）
let animationFrameId = null
let intervalId = null
let workerTimer = null
let lastUpdateTime = 0
const UPDATE_INTERVAL = 100 // 100ms更新一次，提高精度

// 计算样式
const computedValueStyle = computed(() => ({
  fontFamily: 'monospace', // 使用等宽字体确保数字对齐
  ...props.valueStyle
}))

// 获取目标时间的dayjs对象
const getTargetTime = () => {
  if (typeof props.value === 'number') {
    return dayjs(props.value).tz(props.timezone)
  } else if (props.value && typeof props.value === 'object') {
    return dayjs(props.value).tz(props.timezone)
  }
  return null
}

// 格式化时间显示
const formatTime = (duration) => {
  if (duration <= 0) {
    // 倒计时结束，根据format返回对应的零值
    const zeroFormat = props.format
      .replace(/HH|H/g, '00')
      .replace(/mm|m/g, '00')
      .replace(/ss|s/g, '00')
      .replace(/SSS|SS|S/g, '000')
    return zeroFormat
  }

  const hours = Math.floor(duration / (1000 * 60 * 60))
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((duration % (1000 * 60)) / 1000)
  const milliseconds = duration % 1000

  return props.format
    .replace(/HH/g, hours.toString().padStart(2, '0'))
    .replace(/H/g, hours.toString())
    .replace(/mm/g, minutes.toString().padStart(2, '0'))
    .replace(/m/g, minutes.toString())
    .replace(/ss/g, seconds.toString().padStart(2, '0'))
    .replace(/s/g, seconds.toString())
    .replace(/SSS/g, milliseconds.toString().padStart(3, '0'))
    .replace(/SS/g, Math.floor(milliseconds / 10).toString().padStart(2, '0'))
    .replace(/S/g, Math.floor(milliseconds / 100).toString())
}

// 更新倒计时显示
const updateCountdown = () => {
  const targetTime = getTargetTime()
  if (!targetTime) return

  const now = dayjs().tz(props.timezone)
  const duration = targetTime.valueOf() - now.valueOf()

  displayTime.value = formatTime(duration)

  // 检查是否完成
  if (duration <= 0 && !isFinished.value) {
    isFinished.value = true
    emit('finish')
    stopTimer()
  }
}

// 高精度定时器 - 使用Web Worker（如果支持）
const createWorkerTimer = () => {
  if (typeof Worker !== 'undefined') {
    try {
      const workerCode = `
        let intervalId;
        self.onmessage = function(e) {
          if (e.data.action === 'start') {
            intervalId = setInterval(() => {
              self.postMessage({ type: 'tick', instanceId: e.data.instanceId });
            }, ${UPDATE_INTERVAL});
          } else if (e.data.action === 'stop') {
            if (intervalId) {
              clearInterval(intervalId);
              intervalId = null;
            }
          }
        };
      `
      const blob = new Blob([workerCode], { type: 'application/javascript' })
      workerTimer = new Worker(URL.createObjectURL(blob))

      workerTimer.onmessage = (e) => {
        if (e.data.type === 'tick' && e.data.instanceId === instanceId && isActive.value) {
          updateCountdown()
        }
      }

      return true
    } catch (error) {
      console.warn(`实例${instanceId}: Web Worker创建失败，使用备用定时器:`, error)
      return false
    }
  }
  return false
}

// 启动定时器
const startTimer = () => {
  if (!isActive.value) return

  stopTimer() // 确保清理之前的定时器

  // 立即更新一次
  updateCountdown()

  // 尝试使用Web Worker定时器
  if (createWorkerTimer()) {
    workerTimer.postMessage({ action: 'start', instanceId })
  } else {
    // 备用方案：使用requestAnimationFrame + setInterval组合
    const tick = () => {
      if (!isActive.value) return

      const now = performance.now()
      if (now - lastUpdateTime >= UPDATE_INTERVAL) {
        updateCountdown()
        lastUpdateTime = now
      }
      if (!isFinished.value && isActive.value) {
        animationFrameId = requestAnimationFrame(tick)
      }
    }

    // 同时使用setInterval作为备用
    intervalId = setInterval(() => {
      if (isActive.value) {
        updateCountdown()
      }
    }, UPDATE_INTERVAL)
    animationFrameId = requestAnimationFrame(tick)
  }
}

// 停止定时器
const stopTimer = () => {
  isActive.value = false

  // 清理Web Worker定时器
  if (workerTimer) {
    workerTimer.postMessage({ action: 'stop' })
    workerTimer.terminate()
    workerTimer = null
  }

  // 清理requestAnimationFrame
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = null
  }

  // 清理setInterval
  if (intervalId) {
    clearInterval(intervalId)
    intervalId = null
  }
}

// 页面可见性变化处理 - 防止后台运行时计时不准（每个实例独立处理）
const handleVisibilityChange = () => {
  const storageKey = `countdown_hidden_time_${instanceId}`

  if (document.hidden) {
    // 页面隐藏时记录时间
    localStorage.setItem(storageKey, Date.now().toString())
  } else {
    // 页面显示时重新校准
    const hiddenTime = localStorage.getItem(storageKey)
    if (hiddenTime && isActive.value) {
      localStorage.removeItem(storageKey)
      // 立即更新显示，确保时间准确
      updateCountdown()
    }
  }
}

// 窗口焦点处理
const handleWindowFocus = () => {
  if (isActive.value) {
    updateCountdown()
  }
}

const handleWindowBlur = () => {
  const storageKey = `countdown_blur_time_${instanceId}`
  localStorage.setItem(storageKey, Date.now().toString())
}

// 监听props变化
watch(() => props.value, () => {
  if (isActive.value) {
    isFinished.value = false
    startTimer()
  }
}, { immediate: false })

watch(() => props.timezone, () => {
  if (isActive.value) {
    updateCountdown()
  }
})

// 生命周期钩子
onMounted(() => {
  isActive.value = true

  // 添加页面可见性监听
  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 添加窗口焦点监听
  window.addEventListener('focus', handleWindowFocus)
  window.addEventListener('blur', handleWindowBlur)

  startTimer()
})

onUnmounted(() => {
  isActive.value = false
  stopTimer()

  // 清理事件监听
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('focus', handleWindowFocus)
  window.removeEventListener('blur', handleWindowBlur)

  // 清理localStorage（实例特定的）
  const hiddenStorageKey = `countdown_hidden_time_${instanceId}`
  const blurStorageKey = `countdown_blur_time_${instanceId}`
  localStorage.removeItem(hiddenStorageKey)
  localStorage.removeItem(blurStorageKey)
})
</script>

<style scoped>
.countdown-container {
  display: inline-block;
}
</style>
