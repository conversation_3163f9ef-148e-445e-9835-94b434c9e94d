# Countdown 倒计时组件使用示例

## 🚀 快速开始

### 1. 基础导入
```vue
<script setup>
import Countdown from './components/Countdown.vue'
import dayjs from 'dayjs'
</script>
```

### 2. 简单使用
```vue
<template>
  <Countdown
    :value="1756742400000"
    format="HH:mm:ss"
    @finish="handleFinish"
  />
</template>

<script setup>
import Countdown from './components/Countdown.vue'

const handleFinish = () => {
  console.log('倒计时结束！')
}
</script>
```

## 📝 详细示例

### 示例1：基础倒计时
```vue
<template>
  <div>
    <h3>距离新年还有：</h3>
    <Countdown
      :value="newYearTime"
      format="HH 时 mm 分 ss 秒"
      :valueStyle="{ fontSize: '24px', fontWeight: 'bold', color: '#e74c3c' }"
      @finish="onNewYear"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import dayjs from 'dayjs'
import Countdown from './components/Countdown.vue'

// 设置新年时间
const newYearTime = ref(dayjs('2025-01-01 00:00:00').valueOf())

const onNewYear = () => {
  alert('新年快乐！🎉')
}
</script>
```

### 示例2：多个倒计时并发
```vue
<template>
  <div class="countdown-list">
    <div class="countdown-item">
      <h4>会议开始</h4>
      <Countdown
        :value="meetingTime"
        format="mm:ss"
        :valueStyle="{ fontSize: '18px', color: '#3498db' }"
        @finish="() => handleEvent('meeting')"
      />
    </div>
    
    <div class="countdown-item">
      <h4>午休结束</h4>
      <Countdown
        :value="lunchEndTime"
        format="HH:mm:ss"
        :valueStyle="{ fontSize: '16px', color: '#27ae60' }"
        @finish="() => handleEvent('lunch')"
      />
    </div>
    
    <div class="countdown-item">
      <h4>项目截止</h4>
      <Countdown
        :value="deadlineTime"
        format="HH 时 mm 分"
        :valueStyle="{ fontSize: '20px', fontWeight: '600', color: '#e67e22' }"
        @finish="() => handleEvent('deadline')"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import dayjs from 'dayjs'
import Countdown from './components/Countdown.vue'

// 设置不同的倒计时目标
const meetingTime = ref(dayjs().add(15, 'minute').valueOf())
const lunchEndTime = ref(dayjs().add(2, 'hour').valueOf())
const deadlineTime = ref(dayjs().add(3, 'day').valueOf())

const handleEvent = (eventType) => {
  switch(eventType) {
    case 'meeting':
      console.log('会议开始了！')
      break
    case 'lunch':
      console.log('午休结束，开始工作！')
      break
    case 'deadline':
      console.log('项目截止时间到了！')
      break
  }
}
</script>

<style scoped>
.countdown-list {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.countdown-item {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  text-align: center;
  min-width: 200px;
}

.countdown-item h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}
</style>
```

### 示例3：高精度毫秒级倒计时
```vue
<template>
  <div class="precision-timer">
    <h3>高精度计时器</h3>
    <Countdown
      :value="preciseEndTime"
      format="ss.SSS 秒"
      :valueStyle="{ 
        fontSize: '28px', 
        fontFamily: 'monospace',
        color: '#9b59b6',
        fontWeight: 'bold'
      }"
      @finish="onPreciseFinish"
    />
    <p>精确到毫秒的倒计时</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import dayjs from 'dayjs'
import Countdown from './components/Countdown.vue'

// 10秒后的精确时间
const preciseEndTime = ref(dayjs().add(10, 'second').valueOf())

const onPreciseFinish = () => {
  const finishTime = dayjs().format('HH:mm:ss.SSS')
  console.log(`精确完成时间: ${finishTime}`)
}
</script>

<style scoped>
.precision-timer {
  text-align: center;
  padding: 30px;
  background: #f8f9fa;
  border-radius: 10px;
}
</style>
```

### 示例4：不同时区倒计时
```vue
<template>
  <div class="timezone-countdown">
    <div class="timezone-item">
      <h4>北京时间</h4>
      <Countdown
        :value="targetTime"
        format="HH:mm:ss"
        timezone="Asia/Shanghai"
        :valueStyle="{ fontSize: '18px', color: '#e74c3c' }"
        @finish="() => handleTimezone('Beijing')"
      />
    </div>
    
    <div class="timezone-item">
      <h4>纽约时间</h4>
      <Countdown
        :value="targetTime"
        format="HH:mm:ss"
        timezone="America/New_York"
        :valueStyle="{ fontSize: '18px', color: '#3498db' }"
        @finish="() => handleTimezone('New York')"
      />
    </div>
    
    <div class="timezone-item">
      <h4>伦敦时间</h4>
      <Countdown
        :value="targetTime"
        format="HH:mm:ss"
        timezone="Europe/London"
        :valueStyle="{ fontSize: '18px', color: '#27ae60' }"
        @finish="() => handleTimezone('London')"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import Countdown from './components/Countdown.vue'

// 扩展dayjs插件
dayjs.extend(utc)
dayjs.extend(timezone)

// 设置统一的目标时间（UTC时间）
const targetTime = ref(dayjs().add(1, 'hour').valueOf())

const handleTimezone = (city) => {
  console.log(`${city} 时区的倒计时结束了！`)
}
</script>

<style scoped>
.timezone-countdown {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.timezone-item {
  padding: 20px;
  border: 2px solid #ecf0f1;
  border-radius: 10px;
  text-align: center;
}

.timezone-item h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}
</style>
```

### 示例5：动态控制倒计时
```vue
<template>
  <div class="dynamic-countdown">
    <h3>动态倒计时控制</h3>
    
    <div class="controls">
      <button @click="setTimer(30)" class="btn">30秒</button>
      <button @click="setTimer(60)" class="btn">1分钟</button>
      <button @click="setTimer(300)" class="btn">5分钟</button>
      <button @click="setCustomTimer" class="btn">自定义</button>
    </div>
    
    <div class="timer-display">
      <Countdown
        v-if="endTime"
        :key="timerKey"
        :value="endTime"
        format="mm 分 ss 秒"
        :valueStyle="{ fontSize: '24px', fontWeight: 'bold', color: currentColor }"
        @finish="onTimerFinish"
      />
      <p v-else>请选择倒计时时长</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import dayjs from 'dayjs'
import Countdown from './components/Countdown.vue'

const endTime = ref(null)
const timerKey = ref(0)
const currentColor = ref('#3498db')

const colors = ['#e74c3c', '#3498db', '#27ae60', '#f39c12', '#9b59b6']

const setTimer = (seconds) => {
  endTime.value = dayjs().add(seconds, 'second').valueOf()
  currentColor.value = colors[Math.floor(Math.random() * colors.length)]
  timerKey.value++ // 强制重新渲染组件
}

const setCustomTimer = () => {
  const minutes = prompt('请输入分钟数:', '10')
  if (minutes && !isNaN(minutes)) {
    setTimer(parseInt(minutes) * 60)
  }
}

const onTimerFinish = () => {
  alert('倒计时结束！')
  endTime.value = null
}
</script>

<style scoped>
.dynamic-countdown {
  text-align: center;
  padding: 30px;
}

.controls {
  margin: 20px 0;
}

.btn {
  margin: 0 10px;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  background: #3498db;
  color: white;
  cursor: pointer;
  font-size: 14px;
}

.btn:hover {
  background: #2980b9;
}

.timer-display {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
```

## 🎨 样式自定义

### 自定义数字样式
```vue
<Countdown
  :value="endTime"
  format="HH:mm:ss"
  :valueStyle="{
    fontSize: '32px',
    fontWeight: 'bold',
    color: '#e74c3c',
    fontFamily: 'Arial, sans-serif',
    textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
    letterSpacing: '2px'
  }"
/>
```

### 响应式样式
```vue
<Countdown
  :value="endTime"
  format="HH:mm:ss"
  :valueStyle="responsiveStyle"
/>

<script setup>
import { computed } from 'vue'

const responsiveStyle = computed(() => ({
  fontSize: window.innerWidth > 768 ? '24px' : '18px',
  fontWeight: 'bold',
  color: '#3498db'
}))
</script>
```

## 🔧 高级配置

### 格式化选项
```javascript
// 支持的格式化符号
'HH:mm:ss'        // 01:23:45
'HH 时 mm 分 ss 秒' // 01 时 23 分 45 秒
'mm:ss'           // 23:45
'ss.SSS'          // 45.123 (毫秒)
'H时m分s秒'        // 1时23分45秒
```

### 时区配置
```javascript
// 常用时区
'Asia/Shanghai'      // 中国标准时间
'America/New_York'   // 美国东部时间
'Europe/London'      // 英国时间
'Asia/Tokyo'         // 日本时间
'UTC'               // 协调世界时
```

## 🐛 常见问题

### Q: 如何确保多个倒计时不会相互影响？
A: 组件内部使用唯一的实例ID，每个实例都有独立的定时器和存储空间。

### Q: 页面切换到后台后倒计时还准确吗？
A: 是的，组件有完整的后台运行校准机制，恢复时会自动校正时间。

### Q: 可以同时运行多少个倒计时实例？
A: 理论上没有限制，但建议单页面不超过20个以保证性能。

### Q: 如何处理倒计时完成事件？
A: 使用 `@finish` 事件监听器，在倒计时结束时会自动触发。
