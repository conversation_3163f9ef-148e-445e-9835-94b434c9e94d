<script setup>
import { ref, onMounted } from 'vue'
import dayjs from 'dayjs'
import Countdown from './components/Countdown.vue'

// 测试数据
const testResults = ref([])
const currentTime = ref('')

// 创建不同的倒计时测试用例
const testCases = ref([
  {
    id: 1,
    name: '短期倒计时 (30秒)',
    endTime: null,
    format: 'HH:mm:ss',
    style: { fontSize: '18px', fontWeight: 'bold', color: '#e74c3c' }
  },
  {
    id: 2,
    name: '中期倒计时 (5分钟)',
    endTime: null,
    format: 'mm分ss秒',
    style: { fontSize: '16px', color: '#3498db' }
  },
  {
    id: 3,
    name: '长期倒计时 (1小时)',
    endTime: null,
    format: 'HH时mm分ss秒',
    style: { fontSize: '20px', fontWeight: '600', color: '#27ae60' }
  },
  {
    id: 4,
    name: '精确到毫秒测试 (10秒)',
    endTime: null,
    format: 'ss.SSS秒',
    style: { fontSize: '14px', color: '#9b59b6', fontFamily: 'monospace' }
  },
  {
    id: 5,
    name: '自定义时区测试 (UTC)',
    endTime: null,
    format: 'HH:mm:ss',
    style: { fontSize: '16px', color: '#f39c12' },
    timezone: 'UTC'
  }
])

// 初始化测试用例的结束时间
const initTestCases = () => {
  const now = dayjs()
  testCases.value[0].endTime = now.add(30, 'second').valueOf()
  testCases.value[1].endTime = now.add(5, 'minute').valueOf()
  testCases.value[2].endTime = now.add(1, 'hour').valueOf()
  testCases.value[3].endTime = now.add(10, 'second').valueOf()
  testCases.value[4].endTime = now.add(2, 'minute').valueOf()
}

// 更新当前时间显示
const updateCurrentTime = () => {
  currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
}

// 倒计时完成处理
const handleFinish = (testId, testName) => {
  const finishTime = dayjs().format('HH:mm:ss.SSS')
  testResults.value.push({
    id: testId,
    name: testName,
    finishTime,
    timestamp: Date.now()
  })
  console.log(`倒计时完成: ${testName} at ${finishTime}`)
}

// 重置所有测试
const resetAllTests = () => {
  testResults.value = []
  initTestCases()
}

// 添加新的倒计时测试
const addCustomTest = () => {
  const seconds = prompt('请输入倒计时秒数:', '60')
  if (seconds && !isNaN(seconds)) {
    const newTest = {
      id: Date.now(),
      name: `自定义倒计时 (${seconds}秒)`,
      endTime: dayjs().add(parseInt(seconds), 'second').valueOf(),
      format: 'mm:ss',
      style: { fontSize: '16px', color: '#34495e' }
    }
    testCases.value.push(newTest)
  }
}

onMounted(() => {
  initTestCases()
  updateCurrentTime()

  // 每秒更新当前时间显示
  setInterval(updateCurrentTime, 1000)
})
</script>

<template>
  <div class="test-container">
    <header class="header">
      <h1>🕐 高精度倒计时组件测试页面</h1>
      <p class="current-time">当前时间: {{ currentTime }}</p>
      <div class="controls">
        <button @click="resetAllTests" class="btn btn-primary">重置所有测试</button>
        <button @click="addCustomTest" class="btn btn-secondary">添加自定义测试</button>
      </div>
    </header>

    <main class="main-content">
      <!-- 多实例倒计时测试区域 -->
      <section class="test-section">
        <h2>📊 多实例倒计时测试</h2>
        <div class="countdown-grid">
          <div
            v-for="testCase in testCases"
            :key="testCase.id"
            class="countdown-card"
          >
            <h3>{{ testCase.name }}</h3>
            <div class="countdown-display">
              <Countdown
                :value="testCase.endTime"
                :format="testCase.format"
                :valueStyle="testCase.style"
                :timezone="testCase.timezone || 'Asia/Shanghai'"
                @finish="() => handleFinish(testCase.id, testCase.name)"
              />
            </div>
            <p class="test-info">
              目标时间: {{ dayjs(testCase.endTime).format('HH:mm:ss') }}
            </p>
          </div>
        </div>
      </section>

      <!-- 测试结果显示区域 -->
      <section class="results-section" v-if="testResults.length > 0">
        <h2>✅ 完成记录</h2>
        <div class="results-list">
          <div
            v-for="result in testResults"
            :key="result.id"
            class="result-item"
          >
            <span class="result-name">{{ result.name }}</span>
            <span class="result-time">完成时间: {{ result.finishTime }}</span>
          </div>
        </div>
      </section>

      <!-- 性能测试说明 -->
      <section class="info-section">
        <h2>🔬 测试说明</h2>
        <div class="info-content">
          <h3>测试项目：</h3>
          <ul>
            <li><strong>多实例并发：</strong> 同时运行多个倒计时实例，验证相互独立性</li>
            <li><strong>精度测试：</strong> 验证倒计时的准确性和完成时间的精确性</li>
            <li><strong>后台运行：</strong> 切换浏览器标签页测试后台运行准确性</li>
            <li><strong>长时间运行：</strong> 测试长期运行的稳定性</li>
            <li><strong>时区支持：</strong> 验证不同时区的正确处理</li>
          </ul>

          <h3>测试方法：</h3>
          <ol>
            <li>观察多个倒计时是否同步准确运行</li>
            <li>切换到其他标签页，等待一段时间后切回，检查时间是否准确</li>
            <li>最小化浏览器窗口，恢复后检查时间准确性</li>
            <li>对比系统时间验证倒计时完成的准确性</li>
          </ol>
        </div>
      </section>
    </main>
  </div>
</template>

<style scoped>
.test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.header h1 {
  margin: 0 0 15px 0;
  font-size: 2.5em;
  font-weight: 700;
}

.current-time {
  font-size: 1.2em;
  margin: 15px 0;
  opacity: 0.9;
  font-family: 'Courier New', monospace;
}

.controls {
  margin-top: 20px;
}

.btn {
  padding: 12px 24px;
  margin: 0 10px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
  transform: translateY(-2px);
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.test-section h2,
.results-section h2,
.info-section h2 {
  color: #2c3e50;
  border-bottom: 3px solid #3498db;
  padding-bottom: 10px;
  margin-bottom: 25px;
}

.countdown-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.countdown-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border: 1px solid #e1e8ed;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.countdown-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.countdown-card h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3em;
  text-align: center;
}

.countdown-display {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 15px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-info {
  text-align: center;
  color: #7f8c8d;
  font-size: 0.9em;
  margin: 0;
}

.results-section {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 12px;
  border-left: 5px solid #27ae60;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.result-name {
  font-weight: 600;
  color: #2c3e50;
}

.result-time {
  color: #27ae60;
  font-family: 'Courier New', monospace;
}

.info-section {
  background: #fff;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.info-content h3 {
  color: #34495e;
  margin-top: 25px;
  margin-bottom: 15px;
}

.info-content ul,
.info-content ol {
  padding-left: 25px;
}

.info-content li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.info-content strong {
  color: #2c3e50;
}

@media (max-width: 768px) {
  .countdown-grid {
    grid-template-columns: 1fr;
  }

  .header h1 {
    font-size: 2em;
  }

  .result-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
