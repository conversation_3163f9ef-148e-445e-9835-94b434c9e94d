<script setup>
  import Countdown from './components/Countdown.vue'

  const timeFinish = () => {
    console.log('倒计时结束',new Date())
  }
</script>

<template>
  <div>
         <Countdown
              :value="1756711544000"
              format="HH 时 mm 分 ss 秒"
              :valueStyle="{ fontSize: `20px`, fontWeight: 600 }"
              @finish="timeFinish"
            />
  </div>

</template>

<style scoped>

</style>
